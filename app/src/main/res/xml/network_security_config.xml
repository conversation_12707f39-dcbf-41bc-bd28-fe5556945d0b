<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <domain-config cleartextTrafficPermitted="false">
        <!-- Allow AWS Bedrock endpoints -->
        <domain includeSubdomains="true">amazonaws.com</domain>
        <domain includeSubdomains="true">bedrock-runtime.us-east-1.amazonaws.com</domain>
        <domain includeSubdomains="true">bedrock-runtime.us-west-2.amazonaws.com</domain>
        <domain includeSubdomains="true">bedrock-runtime.eu-west-1.amazonaws.com</domain>
        <!-- Add other AWS regions as needed -->
    </domain-config>
    
    <!-- Base configuration for all other domains -->
    <base-config cleartextTrafficPermitted="false">
        <trust-anchors>
            <!-- Trust system certificate authorities -->
            <certificates src="system"/>
        </trust-anchors>
    </base-config>
</network-security-config>
