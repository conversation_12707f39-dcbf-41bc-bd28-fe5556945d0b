package com.emathias.periodic.debug

import android.os.Bundle
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.lifecycle.lifecycleScope
import com.emathias.periodic.config.AwsCredentialManager
import com.emathias.periodic.util.NetworkUtils
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import javax.inject.Inject

@AndroidEntryPoint
class NetworkDebugActivity : ComponentActivity() {
    
    @Inject
    lateinit var credentialManager: AwsCredentialManager
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        setContent {
            NetworkDebugScreen(credentialManager)
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun NetworkDebugScreen(credentialManager: AwsCredentialManager) {
    val context = LocalContext.current
    var debugOutput by remember { mutableStateOf("Network Debug Output:\n\n") }
    var isLoading by remember { mutableStateOf(false) }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "AWS Network Debug Tool",
            style = MaterialTheme.typography.headlineMedium
        )
        
        Row(
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Button(
                onClick = {
                    isLoading = true
                    runNetworkTests(context, credentialManager) { result ->
                        debugOutput += result + "\n"
                        isLoading = false
                    }
                },
                enabled = !isLoading
            ) {
                Text("Run Network Tests")
            }
            
            Button(
                onClick = { debugOutput = "Network Debug Output:\n\n" }
            ) {
                Text("Clear")
            }
        }
        
        if (isLoading) {
            LinearProgressIndicator(
                modifier = Modifier.fillMaxWidth()
            )
        }
        
        Card(
            modifier = Modifier.fillMaxSize()
        ) {
            Text(
                text = debugOutput,
                modifier = Modifier
                    .padding(16.dp)
                    .verticalScroll(rememberScrollState()),
                style = MaterialTheme.typography.bodySmall
            )
        }
    }
}

private fun runNetworkTests(
    context: android.content.Context,
    credentialManager: AwsCredentialManager,
    onResult: (String) -> Unit
) {
    kotlinx.coroutines.CoroutineScope(kotlinx.coroutines.Dispatchers.Main).launch {
        try {
            // Test 1: Basic network availability
            onResult("=== Basic Network Test ===")
            val isNetworkAvailable = NetworkUtils.isNetworkAvailable(context)
            onResult("Network Available: $isNetworkAvailable")
            onResult("Network Type: ${NetworkUtils.getNetworkInfo(context)}")
            
            // Test 2: DNS connectivity
            onResult("\n=== DNS Connectivity Test ===")
            val dnsResult = NetworkUtils.testDnsConnectivity()
            onResult(dnsResult.getOrElse { "DNS test failed: ${it.message}" })
            
            // Test 3: AWS Bedrock DNS resolution
            onResult("\n=== AWS Bedrock DNS Test ===")
            val region = credentialManager.getRegion()
            onResult("Testing region: $region")
            val awsResult = NetworkUtils.testAwsBedrockConnectivity(region)
            onResult(awsResult.getOrElse { "AWS DNS test failed: ${it.message}" })
            
            // Test 4: Credentials check
            onResult("\n=== AWS Credentials Test ===")
            val credsConfigured = credentialManager.areCredentialsConfigured()
            onResult("Credentials configured: $credsConfigured")
            onResult("Region: ${credentialManager.getRegion()}")
            
            onResult("\n=== Test Complete ===")
            
        } catch (e: Exception) {
            onResult("Error running tests: ${e.message}")
            Log.e("NetworkDebug", "Error running network tests", e)
        }
    }
}
