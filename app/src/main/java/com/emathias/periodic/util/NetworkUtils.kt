package com.emathias.periodic.util

import android.content.Context
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.net.InetAddress
import java.net.UnknownHostException

object NetworkUtils {
    private const val TAG = "NetworkUtils"

    /**
     * Check if the device has an active internet connection
     */
    fun isNetworkAvailable(context: Context): Boolean {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val network = connectivityManager.activeNetwork ?: return false
        val networkCapabilities = connectivityManager.getNetworkCapabilities(network) ?: return false
        
        return networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) &&
                networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)
    }

    /**
     * Test DNS resolution for AWS Bedrock endpoints
     */
    suspend fun testAwsBedrockConnectivity(region: String = "us-east-1"): Result<String> = withContext(Dispatchers.IO) {
        try {
            val hostname = "bedrock-runtime.$region.amazonaws.com"
            Log.d(TAG, "Testing DNS resolution for: $hostname")
            
            val address = InetAddress.getByName(hostname)
            val resolvedIp = address.hostAddress
            
            Log.d(TAG, "Successfully resolved $hostname to $resolvedIp")
            Result.success("DNS resolution successful: $hostname -> $resolvedIp")
        } catch (e: UnknownHostException) {
            Log.e(TAG, "Failed to resolve AWS Bedrock hostname", e)
            Result.failure(e)
        } catch (e: Exception) {
            Log.e(TAG, "Unexpected error during DNS test", e)
            Result.failure(e)
        }
    }

    /**
     * Test connectivity to common DNS servers
     */
    suspend fun testDnsConnectivity(): Result<String> = withContext(Dispatchers.IO) {
        val dnsServers = listOf(
            "*******" to "Google DNS",
            "*******" to "Cloudflare DNS",
            "**************" to "OpenDNS"
        )
        
        val results = mutableListOf<String>()
        
        for ((dns, name) in dnsServers) {
            try {
                val address = InetAddress.getByName(dns)
                if (address.isReachable(5000)) {
                    results.add("✓ $name ($dns) - Reachable")
                    Log.d(TAG, "$name is reachable")
                } else {
                    results.add("✗ $name ($dns) - Not reachable")
                    Log.w(TAG, "$name is not reachable")
                }
            } catch (e: Exception) {
                results.add("✗ $name ($dns) - Error: ${e.message}")
                Log.e(TAG, "Error testing $name", e)
            }
        }
        
        Result.success(results.joinToString("\n"))
    }

    /**
     * Get network type information
     */
    fun getNetworkInfo(context: Context): String {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val network = connectivityManager.activeNetwork
        val networkCapabilities = connectivityManager.getNetworkCapabilities(network)
        
        return when {
            networkCapabilities == null -> "No active network"
            networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> "WiFi"
            networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> "Cellular"
            networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) -> "Ethernet"
            else -> "Unknown network type"
        }
    }
}
